{"name": "egg-socket.io", "version": "2.1.0", "description": "egg plugin for socket.io", "eggPlugin": {"name": "io"}, "keywords": ["egg", "eggPlugin", "egg-plugin", "socket.io", "websocket"], "dependencies": {"co": "^4.6.0", "debug": "^3.0.0", "delegates": "^1.0.0", "is-type-of": "^1.0.0", "koa-compose": "^2.5.1", "socket.io": "^2.0.3", "socket.io-redis": "^5.1.0"}, "devDependencies": {"autod": "^2.8.0", "egg": "^1.4.0", "egg-bin": "^4.1.0", "egg-ci": "^1.7.0", "egg-mock": "^3.7.2", "eslint": "^4.4.1", "eslint-config-egg": "^5.0.0", "pedding": "^1.1.0", "rimraf": "^2.6.1", "semver": "^5.4.1", "socket.io-client": "^2.0.3", "supertest": "^3.0.0", "uws": "^8.14.1", "webstorm-disable-index": "^1.2.0"}, "engines": {"node": ">=6.0.0"}, "scripts": {"test": "npm run lint -- --fix && npm run test-local", "test-local": "egg-bin test", "cov": "egg-bin cov", "lint": "eslint .", "ci": "egg-bin pkgfiles --check && npm run lint && npm run cov", "autod": "autod"}, "files": ["app", "lib", "config", "app.js"], "ci": {"version": "6, 7, 8", "license": true, "services": "redis-server"}, "repository": {"type": "git", "url": "git+https://github.com/eggjs/egg-socket.io.git"}, "bugs": {"url": "https://github.com/eggjs/egg/issues"}, "homepage": "https://github.com/eggjs/egg-socket.io#readme", "author": "ngot <<EMAIL>>", "license": "MIT"}