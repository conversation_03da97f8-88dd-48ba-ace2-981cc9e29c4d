[{"name": "Process Start", "start": 1754150963771, "end": 1754150964575, "duration": 804, "pid": 45408, "index": 0}, {"name": "Application Start", "start": 1754150964576, "end": 1754150964855, "duration": 279, "pid": 45408, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1754150964588, "end": 1754150964605, "duration": 17, "pid": 45408, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1754150964605, "end": 1754150964631, "duration": 26, "pid": 45408, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1754150964606, "end": 1754150964606, "duration": 0, "pid": 45408, "index": 4}, {"name": "Require(1) node_modules/egg-session/config/config.default.js", "start": 1754150964608, "end": 1754150964608, "duration": 0, "pid": 45408, "index": 5}, {"name": "Require(2) node_modules/egg-security/config/config.default.js", "start": 1754150964609, "end": 1754150964609, "duration": 0, "pid": 45408, "index": 6}, {"name": "Require(3) node_modules/egg-jsonp/config/config.default.js", "start": 1754150964610, "end": 1754150964611, "duration": 1, "pid": 45408, "index": 7}, {"name": "Require(4) node_modules/egg-onerror/config/config.default.js", "start": 1754150964611, "end": 1754150964612, "duration": 1, "pid": 45408, "index": 8}, {"name": "Require(5) node_modules/egg-i18n/config/config.default.js", "start": 1754150964612, "end": 1754150964612, "duration": 0, "pid": 45408, "index": 9}, {"name": "Require(6) node_modules/egg-watcher/config/config.default.js", "start": 1754150964613, "end": 1754150964613, "duration": 0, "pid": 45408, "index": 10}, {"name": "Require(7) node_modules/egg-schedule/config/config.default.js", "start": 1754150964614, "end": 1754150964615, "duration": 1, "pid": 45408, "index": 11}, {"name": "Require(8) node_modules/egg-multipart/config/config.default.js", "start": 1754150964615, "end": 1754150964616, "duration": 1, "pid": 45408, "index": 12}, {"name": "Require(9) node_modules/egg-development/config/config.default.js", "start": 1754150964616, "end": 1754150964617, "duration": 1, "pid": 45408, "index": 13}, {"name": "Require(10) node_modules/egg-logrotator/config/config.default.js", "start": 1754150964617, "end": 1754150964618, "duration": 1, "pid": 45408, "index": 14}, {"name": "Require(11) node_modules/egg-static/config/config.default.js", "start": 1754150964618, "end": 1754150964619, "duration": 1, "pid": 45408, "index": 15}, {"name": "Require(12) node_modules/egg-view/config/config.default.js", "start": 1754150964619, "end": 1754150964620, "duration": 1, "pid": 45408, "index": 16}, {"name": "Require(13) node_modules/egg-socket.io/config/config.default.js", "start": 1754150964621, "end": 1754150964621, "duration": 0, "pid": 45408, "index": 17}, {"name": "Require(14) node_modules/egg/config/config.default.js", "start": 1754150964621, "end": 1754150964622, "duration": 1, "pid": 45408, "index": 18}, {"name": "Require(15) config/config.default.js", "start": 1754150964623, "end": 1754150964623, "duration": 0, "pid": 45408, "index": 19}, {"name": "Require(16) node_modules/egg-security/config/config.local.js", "start": 1754150964624, "end": 1754150964625, "duration": 1, "pid": 45408, "index": 20}, {"name": "Require(17) node_modules/egg-watcher/config/config.local.js", "start": 1754150964626, "end": 1754150964626, "duration": 0, "pid": 45408, "index": 21}, {"name": "Require(18) node_modules/egg-view/config/config.local.js", "start": 1754150964628, "end": 1754150964629, "duration": 1, "pid": 45408, "index": 22}, {"name": "Require(19) node_modules/egg/config/config.local.js", "start": 1754150964630, "end": 1754150964630, "duration": 0, "pid": 45408, "index": 23}, {"name": "Load extend/agent.js", "start": 1754150964631, "end": 1754150964695, "duration": 64, "pid": 45408, "index": 24}, {"name": "Require(20) node_modules/egg-security/app/extend/agent.js", "start": 1754150964635, "end": 1754150964636, "duration": 1, "pid": 45408, "index": 25}, {"name": "Require(21) node_modules/egg-schedule/app/extend/agent.js", "start": 1754150964638, "end": 1754150964680, "duration": 42, "pid": 45408, "index": 26}, {"name": "Require(22) node_modules/egg-logrotator/app/extend/agent.js", "start": 1754150964683, "end": 1754150964687, "duration": 4, "pid": 45408, "index": 27}, {"name": "Load extend/context.js", "start": 1754150964695, "end": 1754150964750, "duration": 55, "pid": 45408, "index": 28}, {"name": "Require(23) node_modules/egg-security/app/extend/context.js", "start": 1754150964697, "end": 1754150964715, "duration": 18, "pid": 45408, "index": 29}, {"name": "Require(24) node_modules/egg-jsonp/app/extend/context.js", "start": 1754150964716, "end": 1754150964718, "duration": 2, "pid": 45408, "index": 30}, {"name": "Require(25) node_modules/egg-i18n/app/extend/context.js", "start": 1754150964719, "end": 1754150964720, "duration": 1, "pid": 45408, "index": 31}, {"name": "Require(26) node_modules/egg-multipart/app/extend/context.js", "start": 1754150964721, "end": 1754150964739, "duration": 18, "pid": 45408, "index": 32}, {"name": "Require(27) node_modules/egg-view/app/extend/context.js", "start": 1754150964741, "end": 1754150964742, "duration": 1, "pid": 45408, "index": 33}, {"name": "Require(28) node_modules/egg/app/extend/context.js", "start": 1754150964743, "end": 1754150964745, "duration": 2, "pid": 45408, "index": 34}, {"name": "Load agent.js", "start": 1754150964750, "end": 1754150964787, "duration": 37, "pid": 45408, "index": 35}, {"name": "Require(29) node_modules/egg-security/agent.js", "start": 1754150964750, "end": 1754150964751, "duration": 1, "pid": 45408, "index": 36}, {"name": "Require(30) node_modules/egg-onerror/agent.js", "start": 1754150964752, "end": 1754150964752, "duration": 0, "pid": 45408, "index": 37}, {"name": "Require(31) node_modules/egg-watcher/agent.js", "start": 1754150964753, "end": 1754150964756, "duration": 3, "pid": 45408, "index": 38}, {"name": "Require(32) node_modules/egg-schedule/agent.js", "start": 1754150964757, "end": 1754150964759, "duration": 2, "pid": 45408, "index": 39}, {"name": "Require(33) node_modules/egg-development/agent.js", "start": 1754150964760, "end": 1754150964784, "duration": 24, "pid": 45408, "index": 40}, {"name": "Require(34) node_modules/egg-logrotator/agent.js", "start": 1754150964784, "end": 1754150964785, "duration": 1, "pid": 45408, "index": 41}, {"name": "Require(35) node_modules/egg/agent.js", "start": 1754150964786, "end": 1754150964786, "duration": 0, "pid": 45408, "index": 42}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1754150964792, "end": 1754150964851, "duration": 59, "pid": 45408, "index": 43}, {"name": "Before Start in node_modules/egg-schedule/agent.js:12:9", "start": 1754150964793, "end": 1754150964836, "duration": 43, "pid": 45408, "index": 44}, {"name": "Before Start in node_modules/egg-development/agent.js:9:9", "start": 1754150964793, "end": 1754150964855, "duration": 62, "pid": 45408, "index": 45}]