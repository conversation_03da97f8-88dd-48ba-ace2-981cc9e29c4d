{"name": "koa-compose", "description": "compose Koa middleware", "repository": "koajs/compose", "version": "2.5.1", "keywords": ["koa", "middleware", "compose"], "files": ["index.js"], "dependencies": {}, "devDependencies": {"co": "^3.0.0", "istanbul": "^0.4.2", "mocha": "^2.4.5", "should": "^2.0.0"}, "scripts": {"test": "mocha --harmony --require should --reporter spec", "test-cov": "node --harmony ./node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha -- --require should", "test-travis": "node --harmony ./node_modules/.bin/istanbul cover ./node_modules/.bin/_mocha --report lcovonly -- --require should"}, "license": "MIT"}