{"name": "my-egg-project", "version": "1.0.0", "description": "", "private": true, "egg": {"declarations": true}, "dependencies": {"egg": "^3.17.5", "egg-scripts": "2", "egg-socket.io": "^4.1.6"}, "devDependencies": {"egg-bin": "6", "egg-mock": "5", "eslint": "8", "eslint-config-egg": "13"}, "engines": {"node": ">=18.0.0"}, "scripts": {"start": "egg-scripts start --daemon --title=egg-server-my-egg-project", "stop": "egg-scripts stop --title=egg-server-my-egg-project", "dev": "egg-bin dev", "test": "npm run lint -- --fix && npm run test:local", "test:local": "egg-bin test", "cov": "egg-bin cov", "lint": "eslint .", "ci": "npm run lint && npm run cov"}, "repository": {"type": "git", "url": ""}, "author": "", "license": "MIT"}