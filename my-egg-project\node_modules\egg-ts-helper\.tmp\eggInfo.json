{"plugins": {"onerror": {"enable": true, "package": "egg-onerror", "name": "onerror", "dependencies": [], "optionalDependencies": ["jsonp"], "env": [], "from": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\plugin.js", "path": "E:\\Kiro\\my-egg-project\\node_modules\\egg-onerror", "version": "2.4.0"}, "session": {"enable": true, "package": "egg-session", "name": "session", "dependencies": [], "optionalDependencies": [], "env": [], "from": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\plugin.js", "path": "E:\\Kiro\\my-egg-project\\node_modules\\egg-session", "version": "3.3.0"}, "i18n": {"enable": true, "package": "egg-i18n", "name": "i18n", "dependencies": [], "optionalDependencies": [], "env": [], "from": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\plugin.js", "path": "E:\\Kiro\\my-egg-project\\node_modules\\egg-i18n", "version": "2.1.1"}, "watcher": {"enable": true, "package": "egg-watcher", "name": "watcher", "dependencies": [], "optionalDependencies": [], "env": [], "from": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\plugin.js", "path": "E:\\Kiro\\my-egg-project\\node_modules\\egg-watcher", "version": "3.1.1", "dependents": ["development"]}, "multipart": {"enable": true, "package": "egg-multipart", "name": "multipart", "dependencies": [], "optionalDependencies": ["schedule"], "env": [], "from": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\plugin.js", "path": "E:\\Kiro\\my-egg-project\\node_modules\\egg-multipart", "version": "3.5.0"}, "security": {"enable": true, "package": "egg-security", "name": "security", "dependencies": [], "optionalDependencies": ["session"], "env": [], "from": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\plugin.js", "path": "E:\\Kiro\\my-egg-project\\node_modules\\egg-security", "version": "3.7.0"}, "development": {"enable": true, "package": "egg-development", "name": "development", "dependencies": ["watcher"], "optionalDependencies": [], "env": ["local"], "from": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\plugin.js", "path": "E:\\Kiro\\my-egg-project\\node_modules\\egg-development", "version": "3.0.2"}, "logrotator": {"enable": true, "package": "egg-logrotator", "name": "logrotator", "dependencies": ["schedule"], "optionalDependencies": [], "env": [], "from": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\plugin.js", "path": "E:\\Kiro\\my-egg-project\\node_modules\\egg-logrotator", "version": "3.2.0"}, "schedule": {"enable": true, "package": "egg-schedule", "name": "schedule", "dependencies": [], "optionalDependencies": [], "env": [], "from": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\plugin.js", "path": "E:\\Kiro\\my-egg-project\\node_modules\\egg-schedule", "version": "4.0.1", "dependents": ["logrotator"]}, "static": {"enable": true, "package": "egg-static", "name": "static", "dependencies": [], "optionalDependencies": [], "env": [], "from": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\plugin.js", "path": "E:\\Kiro\\my-egg-project\\node_modules\\egg-static", "version": "2.3.1"}, "jsonp": {"enable": true, "package": "egg-jsonp", "name": "jsonp", "dependencies": [], "optionalDependencies": ["security"], "env": [], "from": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\plugin.js", "path": "E:\\Kiro\\my-egg-project\\node_modules\\egg-jsonp", "version": "2.0.0"}, "view": {"enable": true, "package": "egg-view", "name": "view", "dependencies": [], "optionalDependencies": [], "env": [], "from": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\plugin.js", "path": "E:\\Kiro\\my-egg-project\\node_modules\\egg-view", "version": "2.1.4"}, "io": {"enable": true, "package": "egg-socket.io", "name": "io", "dependencies": [], "optionalDependencies": [], "env": [], "from": "E:\\Kiro\\my-egg-project\\config\\plugin.js", "path": "E:\\Kiro\\my-egg-project\\node_modules\\egg-socket.io", "version": "2.1.0"}}, "config": {"coreMiddlewares": [], "coreMiddleware": [], "appMiddlewares": [], "appMiddleware": []}, "eggPaths": ["E:\\Kiro\\my-egg-project\\node_modules\\egg"], "timing": 753}