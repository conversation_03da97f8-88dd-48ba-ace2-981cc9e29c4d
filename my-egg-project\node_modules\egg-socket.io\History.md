
2.1.0 / 2017-08-11
==================

  * feat: add async support for controller. (#20)
  * fix: add the egg required keys config (#19)

2.0.2 / 2017-06-13
==================

  * deps: upgrade (#18)

2.0.1 / 2017-06-12
==================

  * fix: lock socket.io to v2.0.1 fix the error (#17)

2.0.0 / 2017-05-30
==================

  * deps: upgrade socket.io to 2 (#16)

1.2.2 / 2017-05-09
==================

  * fix:when a channel has error must throw the exception.#865 (#14)

1.2.1 / 2017-04-27
==================

  * fix:middleware sometimes not release(#803)
  * test:add ns test case (#12)

1.2.0 / 2017-04-13
==================

  * feat:add engine.io config support (#11)

1.1.0 / 2017-03-22
==================

  * feat:error log (#10)

1.0.2 / 2017-03-15
==================

  * doc:add cluster doc (#9)

1.0.1 / 2017-03-15
==================

  * doc:add nginx conf (#8)

1.0.0 / 2017-03-10
==================

  * doc: fix readme (#7)
  * test:fix windows test (#6)

1.0.0-beta.2 / 2017-03-07
==================

  * test:more test (#5)
  * chore:fix format
  * chore:fix example (#4)

1.0.0-beta.1 / 2017-03-03
==================

  * feat: add session support (#3)
  * doc:update readme (#2)

1.0.0-beta.0 / 2017-03-02
==================

  * feat:first-implement
