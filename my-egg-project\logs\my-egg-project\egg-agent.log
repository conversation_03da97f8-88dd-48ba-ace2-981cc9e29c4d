2025-08-02 23:47:32,290 INFO 25652 [egg:logger] init all loggers with options: {"dir":"E:\\Kiro\\my-egg-project\\logs\\my-egg-project","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"my-egg-project-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"agent","localStorage":{"enabled":false}}
2025-08-02 23:47:32,308 INFO 25652 [egg:core] dump config after load, 8ms
2025-08-02 23:47:32,406 INFO 25652 [egg-watcher] Start watching: ["E:\\Kiro\\my-egg-project\\app","E:\\Kiro\\my-egg-project\\config","E:\\Kiro\\my-egg-project\\mocks","E:\\Kiro\\my-egg-project\\mocks_proxy","E:\\Kiro\\my-egg-project\\app.js"]
2025-08-02 23:47:32,406 INFO 25652 [egg-watcher] Start watching: "E:\\Kiro\\my-egg-project\\app"
2025-08-02 23:47:32,407 INFO 25652 [egg-watcher] Start watching: "E:\\Kiro\\my-egg-project\\config"
2025-08-02 23:47:32,407 INFO 25652 [egg-watcher] Start watching: "E:\\Kiro\\my-egg-project\\mocks"
2025-08-02 23:47:32,408 INFO 25652 [egg-watcher] Start watching: "E:\\Kiro\\my-egg-project\\mocks_proxy"
2025-08-02 23:47:32,408 INFO 25652 [egg-watcher] Start watching: "E:\\Kiro\\my-egg-project\\app.js"
2025-08-02 23:47:32,408 INFO 25652 [egg-watcher:agent] watcher start success
2025-08-02 23:47:32,424 INFO 25652 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [1644ms] - #0 Process Start
                                      ▇▇▇▇▇▇▇▇▇▇▇ [503ms] - #1 Application Start
                                      ▇ [36ms] - #2 Load Plugin
                                       ▇ [54ms] - #3 Load Config
                                       ▇ [1ms] - #4 Require(0) config/config.default.js
                                       ▇ [1ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                                       ▇ [1ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                                       ▇ [1ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                                        ▇ [1ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                                        ▇ [1ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                                        ▇ [1ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                                        ▇ [1ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                                        ▇ [1ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                                        ▇ [1ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                                        ▇ [0ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                                        ▇ [1ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                                        ▇ [1ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                                        ▇ [1ms] - #17 Require(13) node_modules/egg/config/config.default.js
                                        ▇ [0ms] - #18 Require(14) config/config.default.js
                                        ▇ [1ms] - #19 Require(15) node_modules/egg-security/config/config.local.js
                                        ▇ [1ms] - #20 Require(16) node_modules/egg-watcher/config/config.local.js
                                        ▇ [1ms] - #21 Require(17) node_modules/egg-view/config/config.local.js
                                        ▇ [1ms] - #22 Require(18) node_modules/egg/config/config.local.js
                                        ▇▇ [99ms] - #23 Load extend/agent.js
                                         ▇ [2ms] - #24 Require(19) node_modules/egg-security/app/extend/agent.js
                                         ▇ [78ms] - #25 Require(20) node_modules/egg-schedule/app/extend/agent.js
                                          ▇ [2ms] - #26 Require(21) node_modules/egg-logrotator/app/extend/agent.js
                                           ▇ [83ms] - #27 Load extend/context.js
                                           ▇ [21ms] - #28 Require(22) node_modules/egg-security/app/extend/context.js
                                           ▇ [4ms] - #29 Require(23) node_modules/egg-jsonp/app/extend/context.js
                                           ▇ [1ms] - #30 Require(24) node_modules/egg-i18n/app/extend/context.js
                                           ▇ [34ms] - #31 Require(25) node_modules/egg-multipart/app/extend/context.js
                                            ▇ [3ms] - #32 Require(26) node_modules/egg-view/app/extend/context.js
                                            ▇ [6ms] - #33 Require(27) node_modules/egg/app/extend/context.js
                                             ▇ [67ms] - #34 Load agent.js
                                             ▇ [1ms] - #35 Require(28) node_modules/egg-security/agent.js
                                             ▇ [1ms] - #36 Require(29) node_modules/egg-onerror/agent.js
                                             ▇ [5ms] - #37 Require(30) node_modules/egg-watcher/agent.js
                                             ▇ [2ms] - #38 Require(31) node_modules/egg-schedule/agent.js
                                             ▇ [48ms] - #39 Require(32) node_modules/egg-development/agent.js
                                              ▇ [1ms] - #40 Require(33) node_modules/egg-logrotator/agent.js
                                              ▇ [1ms] - #41 Require(34) node_modules/egg/agent.js
                                               ▇▇ [113ms] - #42 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                                               ▇ [72ms] - #43 Before Start in node_modules/egg-schedule/agent.js:12:9
                                               ▇▇ [113ms] - #44 Before Start in node_modules/egg-development/agent.js:9:9
2025-08-02 23:47:32,424 INFO 25652 [egg:core] dump config after ready, 9ms
2025-08-02 23:47:34,459 INFO 25652 [egg-watcher] Received a change event from eventSource: {"event":"rename","path":"E:\\Kiro\\my-egg-project\\app\\public","stat":{"dev":4239666462,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":844424930861908,"size":0,"blocks":0,"atimeMs":1754149654457.2534,"mtimeMs":1754149654457.2534,"ctimeMs":1754149654457.2534,"birthtimeMs":1754149654457.2534},"remove":false,"isDirectory":true,"isFile":false}
2025-08-02 23:53:25,681 WARN 25652 [agent:development] reload worker because E:\Kiro\my-egg-project\config\plugin.js change
2025-08-02 23:53:25,469 INFO 25652 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"E:\\Kiro\\my-egg-project\\config\\plugin.js","stat":{"dev":4239666462,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":844424930835315,"size":177,"blocks":0,"atimeMs":1754150005462.3748,"mtimeMs":1754150005462.3748,"ctimeMs":1754150005462.3748,"birthtimeMs":1754149267996.5845},"remove":false,"isDirectory":false,"isFile":true}
2025-08-02 23:53:25,471 INFO 25652 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"E:\\Kiro\\my-egg-project\\config\\plugin.js","stat":{"dev":4239666462,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":844424930835315,"size":177,"blocks":0,"atimeMs":1754150005462.3748,"mtimeMs":1754150005462.3748,"ctimeMs":1754150005462.3748,"birthtimeMs":1754149267996.5845},"remove":false,"isDirectory":false,"isFile":true}
2025-08-02 23:53:25,472 INFO 25652 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"E:\\Kiro\\my-egg-project\\config\\plugin.js","stat":{"dev":4239666462,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":844424930835315,"size":177,"blocks":0,"atimeMs":1754150005462.3748,"mtimeMs":1754150005462.3748,"ctimeMs":1754150005462.3748,"birthtimeMs":1754149267996.5845},"remove":false,"isDirectory":false,"isFile":true}
2025-08-02 23:53:26,922 WARN 25652 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-08-02 23:53:39,399 WARN 25652 [agent:development] reload worker because E:\Kiro\my-egg-project\config\config.default.js change
2025-08-02 23:53:39,184 INFO 25652 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"E:\\Kiro\\my-egg-project\\config\\config.default.js","stat":{"dev":4239666462,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":844424930835314,"size":952,"blocks":8,"atimeMs":1754150019183.3562,"mtimeMs":1754150019183.3562,"ctimeMs":1754150019183.3562,"birthtimeMs":1754149267995.5881},"remove":false,"isDirectory":false,"isFile":true}
2025-08-02 23:53:39,185 INFO 25652 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"E:\\Kiro\\my-egg-project\\config\\config.default.js","stat":{"dev":4239666462,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":844424930835314,"size":952,"blocks":8,"atimeMs":1754150019183.3562,"mtimeMs":1754150019183.3562,"ctimeMs":1754150019183.3562,"birthtimeMs":1754149267995.5881},"remove":false,"isDirectory":false,"isFile":true}
2025-08-02 23:53:39,190 INFO 25652 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"E:\\Kiro\\my-egg-project\\config\\config.default.js","stat":{"dev":4239666462,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":844424930835314,"size":952,"blocks":8,"atimeMs":1754150019183.3562,"mtimeMs":1754150019183.3562,"ctimeMs":1754150019183.3562,"birthtimeMs":1754149267995.5881},"remove":false,"isDirectory":false,"isFile":true}
2025-08-02 23:53:55,555 INFO 25652 [egg-watcher] Received a change event from eventSource: {"event":"rename","path":"E:\\Kiro\\my-egg-project\\app\\io","stat":{"dev":4239666462,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":1688849860967202,"size":0,"blocks":0,"atimeMs":1754150035553.713,"mtimeMs":1754150035553.713,"ctimeMs":1754150035553.713,"birthtimeMs":1754150035553.713},"remove":false,"isDirectory":true,"isFile":false}
2025-08-02 23:53:55,556 INFO 25652 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"E:\\Kiro\\my-egg-project\\app\\io","stat":{"dev":4239666462,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":1688849860967202,"size":0,"blocks":0,"atimeMs":1754150035553.713,"mtimeMs":1754150035553.713,"ctimeMs":1754150035553.713,"birthtimeMs":1754150035553.713},"remove":false,"isDirectory":true,"isFile":false}
2025-08-02 23:54:01,761 INFO 25652 [egg-watcher] Received a change event from eventSource: {"event":"rename","path":"E:\\Kiro\\my-egg-project\\app\\io\\middleware","stat":{"dev":4239666462,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":1970324837704692,"size":0,"blocks":0,"atimeMs":1754150041759.269,"mtimeMs":1754150041759.269,"ctimeMs":1754150041759.269,"birthtimeMs":1754150041758.2854},"remove":false,"isDirectory":true,"isFile":false}
2025-08-02 23:54:01,761 INFO 25652 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"E:\\Kiro\\my-egg-project\\app\\io","stat":{"dev":4239666462,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":1688849860967202,"size":0,"blocks":0,"atimeMs":1754150041760.2693,"mtimeMs":1754150041758.2854,"ctimeMs":1754150041758.2854,"birthtimeMs":1754150035553.713},"remove":false,"isDirectory":true,"isFile":false}
2025-08-02 23:54:01,762 INFO 25652 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"E:\\Kiro\\my-egg-project\\app\\io\\middleware","stat":{"dev":4239666462,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":1970324837704692,"size":0,"blocks":0,"atimeMs":1754150041761.2737,"mtimeMs":1754150041759.269,"ctimeMs":1754150041759.269,"birthtimeMs":1754150041758.2854},"remove":false,"isDirectory":true,"isFile":false}
2025-08-02 23:54:18,315 WARN 25652 [agent:development] reload worker because E:\Kiro\my-egg-project\app\router.js change
2025-08-02 23:54:18,111 INFO 25652 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"E:\\Kiro\\my-egg-project\\app\\router.js","stat":{"dev":4239666462,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":844424930835313,"size":465,"blocks":0,"atimeMs":1754150058105.262,"mtimeMs":1754150058105.262,"ctimeMs":1754150058105.262,"birthtimeMs":1754149267993.5852},"remove":false,"isDirectory":false,"isFile":true}
2025-08-02 23:54:18,112 INFO 25652 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"E:\\Kiro\\my-egg-project\\app\\router.js","stat":{"dev":4239666462,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":844424930835313,"size":465,"blocks":0,"atimeMs":1754150058105.262,"mtimeMs":1754150058105.262,"ctimeMs":1754150058105.262,"birthtimeMs":1754149267993.5852},"remove":false,"isDirectory":false,"isFile":true}
2025-08-02 23:54:18,112 INFO 25652 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"E:\\Kiro\\my-egg-project\\app\\router.js","stat":{"dev":4239666462,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":844424930835313,"size":465,"blocks":0,"atimeMs":1754150058105.262,"mtimeMs":1754150058105.262,"ctimeMs":1754150058105.262,"birthtimeMs":1754149267993.5852},"remove":false,"isDirectory":false,"isFile":true}
2025-08-02 23:57:33,825 INFO 25652 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"E:\\Kiro\\my-egg-project\\config\\config.default.js","stat":{"dev":4239666462,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":844424930835314,"size":1047,"blocks":8,"atimeMs":1754150253817.9988,"mtimeMs":1754150253817.9988,"ctimeMs":1754150253817.9988,"birthtimeMs":1754149267995.5881},"remove":false,"isDirectory":false,"isFile":true}
2025-08-02 23:57:33,825 INFO 25652 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"E:\\Kiro\\my-egg-project\\config\\config.default.js","stat":{"dev":4239666462,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":844424930835314,"size":1047,"blocks":8,"atimeMs":1754150253817.9988,"mtimeMs":1754150253817.9988,"ctimeMs":1754150253817.9988,"birthtimeMs":1754149267995.5881},"remove":false,"isDirectory":false,"isFile":true}
2025-08-02 23:57:33,826 INFO 25652 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"E:\\Kiro\\my-egg-project\\config\\config.default.js","stat":{"dev":4239666462,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":844424930835314,"size":1047,"blocks":8,"atimeMs":1754150253817.9988,"mtimeMs":1754150253817.9988,"ctimeMs":1754150253817.9988,"birthtimeMs":1754149267995.5881},"remove":false,"isDirectory":false,"isFile":true}
2025-08-02 23:57:34,034 WARN 25652 [agent:development] reload worker because E:\Kiro\my-egg-project\config\config.default.js change
2025-08-02 23:59:12,369 INFO 24696 [egg:logger] init all loggers with options: {"dir":"E:\\Kiro\\my-egg-project\\logs\\my-egg-project","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"my-egg-project-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"agent","localStorage":{"enabled":false}}
2025-08-02 23:59:12,377 INFO 24696 [egg:core] dump config after load, 4ms
2025-08-02 23:59:12,424 INFO 24696 [egg-watcher] Start watching: ["E:\\Kiro\\my-egg-project\\app","E:\\Kiro\\my-egg-project\\config","E:\\Kiro\\my-egg-project\\mocks","E:\\Kiro\\my-egg-project\\mocks_proxy","E:\\Kiro\\my-egg-project\\app.js"]
2025-08-02 23:59:12,424 INFO 24696 [egg-watcher] Start watching: "E:\\Kiro\\my-egg-project\\app"
2025-08-02 23:59:12,424 INFO 24696 [egg-watcher] Start watching: "E:\\Kiro\\my-egg-project\\config"
2025-08-02 23:59:12,425 INFO 24696 [egg-watcher] Start watching: "E:\\Kiro\\my-egg-project\\mocks"
2025-08-02 23:59:12,425 INFO 24696 [egg-watcher] Start watching: "E:\\Kiro\\my-egg-project\\mocks_proxy"
2025-08-02 23:59:12,425 INFO 24696 [egg-watcher] Start watching: "E:\\Kiro\\my-egg-project\\app.js"
2025-08-02 23:59:12,426 INFO 24696 [egg-watcher:agent] watcher start success
2025-08-02 23:59:12,439 INFO 24696 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [797ms] - #0 Process Start
                                     ▇▇▇▇▇▇▇▇▇▇▇▇ [268ms] - #1 Application Start
                                     ▇ [17ms] - #2 Load Plugin
                                      ▇ [26ms] - #3 Load Config
                                      ▇ [0ms] - #4 Require(0) config/config.default.js
                                      ▇ [0ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                                      ▇ [0ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                                      ▇ [0ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                                      ▇ [1ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                                      ▇ [1ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                                       ▇ [1ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                                       ▇ [1ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                                       ▇ [0ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                                       ▇ [0ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                                       ▇ [0ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                                       ▇ [0ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                                       ▇ [1ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                                       ▇ [0ms] - #17 Require(13) E:/Kiro/node_modules/egg-socket.io/config/config.default.js
                                       ▇ [0ms] - #18 Require(14) node_modules/egg/config/config.default.js
                                       ▇ [0ms] - #19 Require(15) config/config.default.js
                                       ▇ [0ms] - #20 Require(16) node_modules/egg-security/config/config.local.js
                                       ▇ [0ms] - #21 Require(17) node_modules/egg-watcher/config/config.local.js
                                       ▇ [1ms] - #22 Require(18) node_modules/egg-view/config/config.local.js
                                       ▇ [0ms] - #23 Require(19) node_modules/egg/config/config.local.js
                                       ▇▇ [52ms] - #24 Load extend/agent.js
                                       ▇ [1ms] - #25 Require(20) node_modules/egg-security/app/extend/agent.js
                                        ▇ [39ms] - #26 Require(21) node_modules/egg-schedule/app/extend/agent.js
                                         ▇ [2ms] - #27 Require(22) node_modules/egg-logrotator/app/extend/agent.js
                                          ▇▇ [54ms] - #28 Load extend/context.js
                                          ▇ [14ms] - #29 Require(23) node_modules/egg-security/app/extend/context.js
                                          ▇ [3ms] - #30 Require(24) node_modules/egg-jsonp/app/extend/context.js
                                           ▇ [0ms] - #31 Require(25) node_modules/egg-i18n/app/extend/context.js
                                           ▇ [18ms] - #32 Require(26) node_modules/egg-multipart/app/extend/context.js
                                            ▇ [1ms] - #33 Require(27) node_modules/egg-view/app/extend/context.js
                                            ▇ [2ms] - #34 Require(28) node_modules/egg/app/extend/context.js
                                            ▇ [38ms] - #35 Load agent.js
                                            ▇ [1ms] - #36 Require(29) node_modules/egg-security/agent.js
                                            ▇ [1ms] - #37 Require(30) node_modules/egg-onerror/agent.js
                                            ▇ [4ms] - #38 Require(31) node_modules/egg-watcher/agent.js
                                             ▇ [2ms] - #39 Require(32) node_modules/egg-schedule/agent.js
                                             ▇ [24ms] - #40 Require(33) node_modules/egg-development/agent.js
                                              ▇ [0ms] - #41 Require(34) node_modules/egg-logrotator/agent.js
                                              ▇ [0ms] - #42 Require(35) node_modules/egg/agent.js
                                              ▇▇ [54ms] - #43 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                                              ▇ [36ms] - #44 Before Start in node_modules/egg-schedule/agent.js:12:9
                                              ▇▇ [61ms] - #45 Before Start in node_modules/egg-development/agent.js:9:9
2025-08-02 23:59:12,439 INFO 24696 [egg:core] dump config after ready, 5ms
2025-08-02 23:59:53,760 INFO 47928 [egg:logger] init all loggers with options: {"dir":"E:\\Kiro\\my-egg-project\\logs\\my-egg-project","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"my-egg-project-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"agent","localStorage":{"enabled":false}}
2025-08-02 23:59:53,766 INFO 47928 [egg:core] dump config after load, 3ms
2025-08-02 23:59:53,811 INFO 47928 [egg-watcher] Start watching: ["E:\\Kiro\\my-egg-project\\app","E:\\Kiro\\my-egg-project\\config","E:\\Kiro\\my-egg-project\\mocks","E:\\Kiro\\my-egg-project\\mocks_proxy","E:\\Kiro\\my-egg-project\\app.js"]
2025-08-02 23:59:53,811 INFO 47928 [egg-watcher] Start watching: "E:\\Kiro\\my-egg-project\\app"
2025-08-02 23:59:53,812 INFO 47928 [egg-watcher] Start watching: "E:\\Kiro\\my-egg-project\\config"
2025-08-02 23:59:53,812 INFO 47928 [egg-watcher] Start watching: "E:\\Kiro\\my-egg-project\\mocks"
2025-08-02 23:59:53,812 INFO 47928 [egg-watcher] Start watching: "E:\\Kiro\\my-egg-project\\mocks_proxy"
2025-08-02 23:59:53,812 INFO 47928 [egg-watcher] Start watching: "E:\\Kiro\\my-egg-project\\app.js"
2025-08-02 23:59:53,813 INFO 47928 [egg-watcher:agent] watcher start success
2025-08-02 23:59:53,822 INFO 47928 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [733ms] - #0 Process Start
                                    ▇▇▇▇▇▇▇▇▇▇▇▇▇ [269ms] - #1 Application Start
                                    ▇ [39ms] - #2 Load Plugin
                                      ▇ [30ms] - #3 Load Config
                                       ▇ [1ms] - #4 Require(0) config/config.default.js
                                       ▇ [1ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                                       ▇ [0ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                                       ▇ [1ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                                       ▇ [0ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                                       ▇ [0ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                                       ▇ [0ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                                       ▇ [0ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                                       ▇ [0ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                                       ▇ [1ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                                       ▇ [1ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                                       ▇ [0ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                                       ▇ [0ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                                       ▇ [0ms] - #17 Require(13) E:/Kiro/node_modules/egg-socket.io/config/config.default.js
                                        ▇ [0ms] - #18 Require(14) node_modules/egg/config/config.default.js
                                        ▇ [0ms] - #19 Require(15) config/config.default.js
                                        ▇ [0ms] - #20 Require(16) node_modules/egg-security/config/config.local.js
                                        ▇ [0ms] - #21 Require(17) node_modules/egg-watcher/config/config.local.js
                                        ▇ [0ms] - #22 Require(18) node_modules/egg-view/config/config.local.js
                                        ▇ [1ms] - #23 Require(19) node_modules/egg/config/config.local.js
                                        ▇▇ [48ms] - #24 Load extend/agent.js
                                        ▇ [1ms] - #25 Require(20) node_modules/egg-security/app/extend/agent.js
                                        ▇ [38ms] - #26 Require(21) node_modules/egg-schedule/app/extend/agent.js
                                          ▇ [1ms] - #27 Require(22) node_modules/egg-logrotator/app/extend/agent.js
                                          ▇▇ [44ms] - #28 Load extend/context.js
                                          ▇ [10ms] - #29 Require(23) node_modules/egg-security/app/extend/context.js
                                           ▇ [1ms] - #30 Require(24) node_modules/egg-jsonp/app/extend/context.js
                                           ▇ [0ms] - #31 Require(25) node_modules/egg-i18n/app/extend/context.js
                                           ▇ [17ms] - #32 Require(26) node_modules/egg-multipart/app/extend/context.js
                                            ▇ [2ms] - #33 Require(27) node_modules/egg-view/app/extend/context.js
                                            ▇ [2ms] - #34 Require(28) node_modules/egg/app/extend/context.js
                                             ▇ [34ms] - #35 Load agent.js
                                             ▇ [1ms] - #36 Require(29) node_modules/egg-security/agent.js
                                             ▇ [1ms] - #37 Require(30) node_modules/egg-onerror/agent.js
                                             ▇ [5ms] - #38 Require(31) node_modules/egg-watcher/agent.js
                                             ▇ [3ms] - #39 Require(32) node_modules/egg-schedule/agent.js
                                             ▇ [17ms] - #40 Require(33) node_modules/egg-development/agent.js
                                              ▇ [1ms] - #41 Require(34) node_modules/egg-logrotator/agent.js
                                              ▇ [0ms] - #42 Require(35) node_modules/egg/agent.js
                                               ▇▇ [51ms] - #43 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                                               ▇ [35ms] - #44 Before Start in node_modules/egg-schedule/agent.js:12:9
                                               ▇▇ [55ms] - #45 Before Start in node_modules/egg-development/agent.js:9:9
2025-08-02 23:59:53,822 INFO 47928 [egg:core] dump config after ready, 4ms
2025-08-03 00:00:55,955 INFO 33976 [egg:logger] init all loggers with options: {"dir":"E:\\Kiro\\my-egg-project\\logs\\my-egg-project","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"my-egg-project-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"agent","localStorage":{"enabled":false}}
2025-08-03 00:00:55,963 INFO 33976 [egg:core] dump config after load, 5ms
2025-08-03 00:00:56,010 INFO 33976 [egg-watcher] Start watching: ["E:\\Kiro\\my-egg-project\\app","E:\\Kiro\\my-egg-project\\config","E:\\Kiro\\my-egg-project\\mocks","E:\\Kiro\\my-egg-project\\mocks_proxy","E:\\Kiro\\my-egg-project\\app.js"]
2025-08-03 00:00:56,010 INFO 33976 [egg-watcher] Start watching: "E:\\Kiro\\my-egg-project\\app"
2025-08-03 00:00:56,010 INFO 33976 [egg-watcher] Start watching: "E:\\Kiro\\my-egg-project\\config"
2025-08-03 00:00:56,010 INFO 33976 [egg-watcher] Start watching: "E:\\Kiro\\my-egg-project\\mocks"
2025-08-03 00:00:56,010 INFO 33976 [egg-watcher] Start watching: "E:\\Kiro\\my-egg-project\\mocks_proxy"
2025-08-03 00:00:56,010 INFO 33976 [egg-watcher] Start watching: "E:\\Kiro\\my-egg-project\\app.js"
2025-08-03 00:00:56,011 INFO 33976 [egg-watcher:agent] watcher start success
2025-08-03 00:00:56,023 INFO 33976 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [756ms] - #0 Process Start
                                     ▇▇▇▇▇▇▇▇▇▇▇▇ [255ms] - #1 Application Start
                                     ▇ [17ms] - #2 Load Plugin
                                      ▇ [25ms] - #3 Load Config
                                      ▇ [1ms] - #4 Require(0) config/config.default.js
                                      ▇ [1ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                                      ▇ [1ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                                      ▇ [0ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                                      ▇ [0ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                                       ▇ [0ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                                       ▇ [0ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                                       ▇ [0ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                                       ▇ [1ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                                       ▇ [0ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                                       ▇ [0ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                                       ▇ [0ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                                       ▇ [0ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                                       ▇ [0ms] - #17 Require(13) E:/Kiro/node_modules/egg-socket.io/config/config.default.js
                                       ▇ [1ms] - #18 Require(14) node_modules/egg/config/config.default.js
                                       ▇ [0ms] - #19 Require(15) config/config.default.js
                                       ▇ [1ms] - #20 Require(16) node_modules/egg-security/config/config.local.js
                                       ▇ [0ms] - #21 Require(17) node_modules/egg-watcher/config/config.local.js
                                       ▇ [0ms] - #22 Require(18) node_modules/egg-view/config/config.local.js
                                       ▇ [1ms] - #23 Require(19) node_modules/egg/config/config.local.js
                                       ▇▇ [57ms] - #24 Load extend/agent.js
                                       ▇ [2ms] - #25 Require(20) node_modules/egg-security/app/extend/agent.js
                                        ▇▇ [43ms] - #26 Require(21) node_modules/egg-schedule/app/extend/agent.js
                                          ▇ [2ms] - #27 Require(22) node_modules/egg-logrotator/app/extend/agent.js
                                          ▇▇ [45ms] - #28 Load extend/context.js
                                          ▇ [11ms] - #29 Require(23) node_modules/egg-security/app/extend/context.js
                                           ▇ [2ms] - #30 Require(24) node_modules/egg-jsonp/app/extend/context.js
                                           ▇ [1ms] - #31 Require(25) node_modules/egg-i18n/app/extend/context.js
                                           ▇ [18ms] - #32 Require(26) node_modules/egg-multipart/app/extend/context.js
                                            ▇ [1ms] - #33 Require(27) node_modules/egg-view/app/extend/context.js
                                            ▇ [2ms] - #34 Require(28) node_modules/egg/app/extend/context.js
                                            ▇ [31ms] - #35 Load agent.js
                                            ▇ [0ms] - #36 Require(29) node_modules/egg-security/agent.js
                                             ▇ [0ms] - #37 Require(30) node_modules/egg-onerror/agent.js
                                             ▇ [4ms] - #38 Require(31) node_modules/egg-watcher/agent.js
                                             ▇ [2ms] - #39 Require(32) node_modules/egg-schedule/agent.js
                                             ▇ [19ms] - #40 Require(33) node_modules/egg-development/agent.js
                                              ▇ [0ms] - #41 Require(34) node_modules/egg-logrotator/agent.js
                                              ▇ [1ms] - #42 Require(35) node_modules/egg/agent.js
                                              ▇▇ [55ms] - #43 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                                              ▇ [38ms] - #44 Before Start in node_modules/egg-schedule/agent.js:12:9
                                              ▇▇ [59ms] - #45 Before Start in node_modules/egg-development/agent.js:9:9
2025-08-03 00:00:56,023 INFO 33976 [egg:core] dump config after ready, 5ms
2025-08-03 00:02:26,468 INFO 22012 [egg:logger] init all loggers with options: {"dir":"E:\\Kiro\\my-egg-project\\logs\\my-egg-project","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"my-egg-project-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"agent","localStorage":{"enabled":false}}
2025-08-03 00:02:26,475 INFO 22012 [egg:core] dump config after load, 4ms
2025-08-03 00:02:26,520 INFO 22012 [egg-watcher] Start watching: ["E:\\Kiro\\my-egg-project\\app","E:\\Kiro\\my-egg-project\\config","E:\\Kiro\\my-egg-project\\mocks","E:\\Kiro\\my-egg-project\\mocks_proxy","E:\\Kiro\\my-egg-project\\app.js"]
2025-08-03 00:02:26,520 INFO 22012 [egg-watcher] Start watching: "E:\\Kiro\\my-egg-project\\app"
2025-08-03 00:02:26,521 INFO 22012 [egg-watcher] Start watching: "E:\\Kiro\\my-egg-project\\config"
2025-08-03 00:02:26,521 INFO 22012 [egg-watcher] Start watching: "E:\\Kiro\\my-egg-project\\mocks"
2025-08-03 00:02:26,521 INFO 22012 [egg-watcher] Start watching: "E:\\Kiro\\my-egg-project\\mocks_proxy"
2025-08-03 00:02:26,521 INFO 22012 [egg-watcher] Start watching: "E:\\Kiro\\my-egg-project\\app.js"
2025-08-03 00:02:26,522 INFO 22012 [egg-watcher:agent] watcher start success
2025-08-03 00:02:26,531 INFO 22012 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [772ms] - #0 Process Start
                                     ▇▇▇▇▇▇▇▇▇▇▇▇ [250ms] - #1 Application Start
                                      ▇ [17ms] - #2 Load Plugin
                                       ▇ [24ms] - #3 Load Config
                                       ▇ [0ms] - #4 Require(0) config/config.default.js
                                       ▇ [0ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                                       ▇ [0ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                                       ▇ [1ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                                       ▇ [1ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                                       ▇ [1ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                                       ▇ [1ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                                       ▇ [1ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                                       ▇ [0ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                                       ▇ [0ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                                       ▇ [0ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                                       ▇ [1ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                                       ▇ [1ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                                       ▇ [0ms] - #17 Require(13) node_modules/egg-socket.io/config/config.default.js
                                       ▇ [0ms] - #18 Require(14) node_modules/egg/config/config.default.js
                                        ▇ [0ms] - #19 Require(15) config/config.default.js
                                        ▇ [0ms] - #20 Require(16) node_modules/egg-security/config/config.local.js
                                        ▇ [1ms] - #21 Require(17) node_modules/egg-watcher/config/config.local.js
                                        ▇ [1ms] - #22 Require(18) node_modules/egg-view/config/config.local.js
                                        ▇ [0ms] - #23 Require(19) node_modules/egg/config/config.local.js
                                        ▇▇ [50ms] - #24 Load extend/agent.js
                                        ▇ [1ms] - #25 Require(20) node_modules/egg-security/app/extend/agent.js
                                        ▇ [38ms] - #26 Require(21) node_modules/egg-schedule/app/extend/agent.js
                                          ▇ [2ms] - #27 Require(22) node_modules/egg-logrotator/app/extend/agent.js
                                          ▇▇ [51ms] - #28 Load extend/context.js
                                          ▇ [13ms] - #29 Require(23) node_modules/egg-security/app/extend/context.js
                                           ▇ [3ms] - #30 Require(24) node_modules/egg-jsonp/app/extend/context.js
                                           ▇ [1ms] - #31 Require(25) node_modules/egg-i18n/app/extend/context.js
                                           ▇ [22ms] - #32 Require(26) node_modules/egg-multipart/app/extend/context.js
                                            ▇ [1ms] - #33 Require(27) node_modules/egg-view/app/extend/context.js
                                             ▇ [1ms] - #34 Require(28) node_modules/egg/app/extend/context.js
                                             ▇ [31ms] - #35 Load agent.js
                                             ▇ [0ms] - #36 Require(29) node_modules/egg-security/agent.js
                                             ▇ [0ms] - #37 Require(30) node_modules/egg-onerror/agent.js
                                             ▇ [4ms] - #38 Require(31) node_modules/egg-watcher/agent.js
                                             ▇ [2ms] - #39 Require(32) node_modules/egg-schedule/agent.js
                                             ▇ [18ms] - #40 Require(33) node_modules/egg-development/agent.js
                                              ▇ [0ms] - #41 Require(34) node_modules/egg-logrotator/agent.js
                                              ▇ [0ms] - #42 Require(35) node_modules/egg/agent.js
                                               ▇▇ [52ms] - #43 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                                               ▇ [37ms] - #44 Before Start in node_modules/egg-schedule/agent.js:12:9
                                               ▇▇ [56ms] - #45 Before Start in node_modules/egg-development/agent.js:9:9
2025-08-03 00:02:26,531 INFO 22012 [egg:core] dump config after ready, 4ms
2025-08-03 00:04:50,796 INFO 23368 [egg:logger] init all loggers with options: {"dir":"E:\\Kiro\\my-egg-project\\logs\\my-egg-project","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"my-egg-project-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"agent","localStorage":{"enabled":false}}
2025-08-03 00:04:50,803 INFO 23368 [egg:core] dump config after load, 4ms
2025-08-03 00:04:50,842 INFO 23368 [egg-watcher] Start watching: ["E:\\Kiro\\my-egg-project\\app","E:\\Kiro\\my-egg-project\\config","E:\\Kiro\\my-egg-project\\mocks","E:\\Kiro\\my-egg-project\\mocks_proxy","E:\\Kiro\\my-egg-project\\app.js"]
2025-08-03 00:04:50,842 INFO 23368 [egg-watcher] Start watching: "E:\\Kiro\\my-egg-project\\app"
2025-08-03 00:04:50,843 INFO 23368 [egg-watcher] Start watching: "E:\\Kiro\\my-egg-project\\config"
2025-08-03 00:04:50,843 INFO 23368 [egg-watcher] Start watching: "E:\\Kiro\\my-egg-project\\mocks"
2025-08-03 00:04:50,843 INFO 23368 [egg-watcher] Start watching: "E:\\Kiro\\my-egg-project\\mocks_proxy"
2025-08-03 00:04:50,843 INFO 23368 [egg-watcher] Start watching: "E:\\Kiro\\my-egg-project\\app.js"
2025-08-03 00:04:50,844 INFO 23368 [egg-watcher:agent] watcher start success
2025-08-03 00:04:50,852 INFO 23368 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [685ms] - #0 Process Start
                                     ▇▇▇▇▇▇▇▇▇▇▇ [213ms] - #1 Application Start
                                      ▇ [16ms] - #2 Load Plugin
                                       ▇ [20ms] - #3 Load Config
                                       ▇ [0ms] - #4 Require(0) config/config.default.js
                                       ▇ [0ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                                       ▇ [0ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                                       ▇ [0ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                                       ▇ [0ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                                       ▇ [1ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                                       ▇ [1ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                                       ▇ [1ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                                       ▇ [0ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                                       ▇ [0ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                                       ▇ [0ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                                        ▇ [0ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                                        ▇ [1ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                                        ▇ [1ms] - #17 Require(13) node_modules/egg-socket.io/config/config.default.js
                                        ▇ [0ms] - #18 Require(14) node_modules/egg/config/config.default.js
                                        ▇ [0ms] - #19 Require(15) config/config.default.js
                                        ▇ [1ms] - #20 Require(16) node_modules/egg-security/config/config.local.js
                                        ▇ [0ms] - #21 Require(17) node_modules/egg-watcher/config/config.local.js
                                        ▇ [0ms] - #22 Require(18) node_modules/egg-view/config/config.local.js
                                        ▇ [0ms] - #23 Require(19) node_modules/egg/config/config.local.js
                                        ▇▇ [42ms] - #24 Load extend/agent.js
                                        ▇ [1ms] - #25 Require(20) node_modules/egg-security/app/extend/agent.js
                                        ▇ [31ms] - #26 Require(21) node_modules/egg-schedule/app/extend/agent.js
                                          ▇ [1ms] - #27 Require(22) node_modules/egg-logrotator/app/extend/agent.js
                                          ▇▇ [43ms] - #28 Load extend/context.js
                                          ▇ [10ms] - #29 Require(23) node_modules/egg-security/app/extend/context.js
                                           ▇ [1ms] - #30 Require(24) node_modules/egg-jsonp/app/extend/context.js
                                           ▇ [1ms] - #31 Require(25) node_modules/egg-i18n/app/extend/context.js
                                           ▇ [19ms] - #32 Require(26) node_modules/egg-multipart/app/extend/context.js
                                            ▇ [1ms] - #33 Require(27) node_modules/egg-view/app/extend/context.js
                                             ▇ [2ms] - #34 Require(28) node_modules/egg/app/extend/context.js
                                             ▇ [26ms] - #35 Load agent.js
                                             ▇ [0ms] - #36 Require(29) node_modules/egg-security/agent.js
                                             ▇ [0ms] - #37 Require(30) node_modules/egg-onerror/agent.js
                                             ▇ [3ms] - #38 Require(31) node_modules/egg-watcher/agent.js
                                             ▇ [1ms] - #39 Require(32) node_modules/egg-schedule/agent.js
                                             ▇ [16ms] - #40 Require(33) node_modules/egg-development/agent.js
                                              ▇ [0ms] - #41 Require(34) node_modules/egg-logrotator/agent.js
                                              ▇ [0ms] - #42 Require(35) node_modules/egg/agent.js
                                               ▇▇ [46ms] - #43 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                                               ▇ [33ms] - #44 Before Start in node_modules/egg-schedule/agent.js:12:9
                                               ▇▇ [49ms] - #45 Before Start in node_modules/egg-development/agent.js:9:9
2025-08-03 00:04:50,852 INFO 23368 [egg:core] dump config after ready, 3ms
2025-08-03 00:09:24,790 INFO 45408 [egg:logger] init all loggers with options: {"dir":"E:\\Kiro\\my-egg-project\\logs\\my-egg-project","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"my-egg-project-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"agent","localStorage":{"enabled":false}}
2025-08-03 00:09:24,798 INFO 45408 [egg:core] dump config after load, 3ms
2025-08-03 00:09:24,849 INFO 45408 [egg-watcher] Start watching: ["E:\\Kiro\\my-egg-project\\app","E:\\Kiro\\my-egg-project\\config","E:\\Kiro\\my-egg-project\\mocks","E:\\Kiro\\my-egg-project\\mocks_proxy","E:\\Kiro\\my-egg-project\\app.js"]
2025-08-03 00:09:24,849 INFO 45408 [egg-watcher] Start watching: "E:\\Kiro\\my-egg-project\\app"
2025-08-03 00:09:24,850 INFO 45408 [egg-watcher] Start watching: "E:\\Kiro\\my-egg-project\\config"
2025-08-03 00:09:24,850 INFO 45408 [egg-watcher] Start watching: "E:\\Kiro\\my-egg-project\\mocks"
2025-08-03 00:09:24,850 INFO 45408 [egg-watcher] Start watching: "E:\\Kiro\\my-egg-project\\mocks_proxy"
2025-08-03 00:09:24,850 INFO 45408 [egg-watcher] Start watching: "E:\\Kiro\\my-egg-project\\app.js"
2025-08-03 00:09:24,851 INFO 45408 [egg-watcher:agent] watcher start success
2025-08-03 00:09:24,859 INFO 45408 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [804ms] - #0 Process Start
                                    ▇▇▇▇▇▇▇▇▇▇▇▇ [279ms] - #1 Application Start
                                     ▇ [17ms] - #2 Load Plugin
                                      ▇ [26ms] - #3 Load Config
                                      ▇ [0ms] - #4 Require(0) config/config.default.js
                                      ▇ [0ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                                      ▇ [0ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                                      ▇ [1ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                                      ▇ [1ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                                      ▇ [0ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                                      ▇ [0ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                                      ▇ [1ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                                      ▇ [1ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                                      ▇ [1ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                                      ▇ [1ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                                      ▇ [1ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                                      ▇ [1ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                                       ▇ [0ms] - #17 Require(13) node_modules/egg-socket.io/config/config.default.js
                                       ▇ [1ms] - #18 Require(14) node_modules/egg/config/config.default.js
                                       ▇ [0ms] - #19 Require(15) config/config.default.js
                                       ▇ [1ms] - #20 Require(16) node_modules/egg-security/config/config.local.js
                                       ▇ [0ms] - #21 Require(17) node_modules/egg-watcher/config/config.local.js
                                       ▇ [1ms] - #22 Require(18) node_modules/egg-view/config/config.local.js
                                       ▇ [0ms] - #23 Require(19) node_modules/egg/config/config.local.js
                                       ▇▇ [64ms] - #24 Load extend/agent.js
                                       ▇ [1ms] - #25 Require(20) node_modules/egg-security/app/extend/agent.js
                                       ▇ [42ms] - #26 Require(21) node_modules/egg-schedule/app/extend/agent.js
                                         ▇ [4ms] - #27 Require(22) node_modules/egg-logrotator/app/extend/agent.js
                                          ▇▇ [55ms] - #28 Load extend/context.js
                                          ▇ [18ms] - #29 Require(23) node_modules/egg-security/app/extend/context.js
                                           ▇ [2ms] - #30 Require(24) node_modules/egg-jsonp/app/extend/context.js
                                           ▇ [1ms] - #31 Require(25) node_modules/egg-i18n/app/extend/context.js
                                           ▇ [18ms] - #32 Require(26) node_modules/egg-multipart/app/extend/context.js
                                            ▇ [1ms] - #33 Require(27) node_modules/egg-view/app/extend/context.js
                                            ▇ [2ms] - #34 Require(28) node_modules/egg/app/extend/context.js
                                            ▇ [37ms] - #35 Load agent.js
                                            ▇ [1ms] - #36 Require(29) node_modules/egg-security/agent.js
                                             ▇ [0ms] - #37 Require(30) node_modules/egg-onerror/agent.js
                                             ▇ [3ms] - #38 Require(31) node_modules/egg-watcher/agent.js
                                             ▇ [2ms] - #39 Require(32) node_modules/egg-schedule/agent.js
                                             ▇ [24ms] - #40 Require(33) node_modules/egg-development/agent.js
                                              ▇ [1ms] - #41 Require(34) node_modules/egg-logrotator/agent.js
                                              ▇ [0ms] - #42 Require(35) node_modules/egg/agent.js
                                              ▇▇ [59ms] - #43 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                                              ▇ [43ms] - #44 Before Start in node_modules/egg-schedule/agent.js:12:9
                                              ▇▇ [62ms] - #45 Before Start in node_modules/egg-development/agent.js:9:9
2025-08-03 00:09:24,859 INFO 45408 [egg:core] dump config after ready, 4ms
